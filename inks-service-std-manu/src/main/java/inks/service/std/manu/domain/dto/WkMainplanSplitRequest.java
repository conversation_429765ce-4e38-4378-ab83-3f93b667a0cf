package inks.service.std.manu.domain.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Positive;
import java.io.Serializable;

/**
 * 生产主计划拆分请求DTO
 *
 * <AUTHOR>
 * @since 2025-01-04
 */
@ApiModel(value = "生产主计划拆分请求", description = "生产主计划拆分请求参数")
public class WkMainplanSplitRequest implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主计划子表ID", required = true)
    @NotBlank(message = "主计划子表ID不能为空")
    private String itemid;

    @ApiModelProperty(value = "拆分数量", required = true)
    @NotNull(message = "拆分数量不能为空")
    @Positive(message = "拆分数量必须大于0")
    private Double splitqty;

    @ApiModelProperty(value = "拆分货品ID", required = true)
    @NotBlank(message = "拆分货品ID不能为空")
    private String splitgoodsid;

    public String getItemid() {
        return itemid;
    }

    public void setItemid(String itemid) {
        this.itemid = itemid;
    }

    public Double getSplitqty() {
        return splitqty;
    }

    public void setSplitqty(Double splitqty) {
        this.splitqty = splitqty;
    }

    public String getSplitgoodsid() {
        return splitgoodsid;
    }

    public void setSplitgoodsid(String splitgoodsid) {
        this.splitgoodsid = splitgoodsid;
    }

    @Override
    public String toString() {
        return "WkMainplanSplitRequest{" +
                "itemId='" + itemid + '\'' +
                ", splitQuantity=" + splitqty +
                ", splitGoodsId='" + splitgoodsid + '\'' +
                '}';
    }
}
